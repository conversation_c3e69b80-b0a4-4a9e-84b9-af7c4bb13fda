'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = {
  language: 'sass',
  init: function init(Prism) {
    (function (Prism) {
      Prism.languages.sass = Prism.languages.extend('css', {
        // Sass comments don't need to be closed, only indented
        comment: {
          pattern: /^([ \t]*)\/[\/*].*(?:(?:\r?\n|\r)\1[ \t]+.+)*/m,
          lookbehind: true
        }
      });

      Prism.languages.insertBefore('sass', 'atrule', {
        // We want to consume the whole line
        'atrule-line': {
          // Includes support for = and + shortcuts
          pattern: /^(?:[ \t]*)[@+=].+/m,
          inside: {
            atrule: /(?:@[\w-]+|[+=])/m
          }
        }
      });
      delete Prism.languages.sass.atrule;

      var variable = /\$[-\w]+|#\{\$[-\w]+\}/;
      var operator = [/[+*\/%]|[=!]=|<=?|>=?|\b(?:and|or|not)\b/, {
        pattern: /(\s+)-(?=\s)/,
        lookbehind: true
      }];

      Prism.languages.insertBefore('sass', 'property', {
        // We want to consume the whole line
        'variable-line': {
          pattern: /^[ \t]*\$.+/m,
          inside: {
            punctuation: /:/,
            variable: variable,
            operator: operator
          }
        },
        // We want to consume the whole line
        'property-line': {
          pattern: /^[ \t]*(?:[^:\s]+ *:.*|:[^:\s]+.*)/m,
          inside: {
            property: [/[^:\s]+(?=\s*:)/, {
              pattern: /(:)[^:\s]+/,
              lookbehind: true
            }],
            punctuation: /:/,
            variable: variable,
            operator: operator,
            important: Prism.languages.sass.important
          }
        }
      });
      delete Prism.languages.sass.property;
      delete Prism.languages.sass.important;

      // Now that whole lines for other patterns are consumed,
      // what's left should be selectors
      delete Prism.languages.sass.selector;
      Prism.languages.insertBefore('sass', 'punctuation', {
        selector: {
          pattern: /([ \t]*)\S(?:,?[^,\r\n]+)*(?:,(?:\r?\n|\r)\1[ \t]+\S(?:,?[^,\r\n]+)*)*/,
          lookbehind: true
        }
      });
    })(Prism);
  }
};