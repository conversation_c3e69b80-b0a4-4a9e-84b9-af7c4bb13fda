# /etc/fstab: static file system information.
#
# Use 'blkid' to print the universally unique identifier for a device; this may
# be used with UUID= as a more robust way to name devices that works even if
# disks are added and removed. See fstab(5).
#
# <file system>             <mount point>  <type>  <options>  <dump>  <pass>

# SISTEMA PRINCIPAL - PARTICIONES ESENCIALES (SIEMPRE NECESARIAS)
UUID=3086117b-6faf-4c37-818c-41e85896f2be /              btrfs   subvol=/@,defaults,noatime,compress=zstd,commit=120 0 0
UUID=3086117b-6faf-4c37-818c-41e85896f2be /home          btrfs   subvol=/@home,defaults,noatime,compress=zstd,commit=120 0 0
UUID=3086117b-6faf-4c37-818c-41e85896f2be /root          btrfs   subvol=/@root,defaults,noatime,compress=zstd,commit=120 0 0
UUID=3086117b-6faf-4c37-818c-41e85896f2be /srv           btrfs   subvol=/@srv,defaults,noatime,compress=zstd,commit=120 0 0
UUID=3086117b-6faf-4c37-818c-41e85896f2be /var/cache     btrfs   subvol=/@cache,defaults,noatime,compress=zstd,commit=120 0 0
UUID=3086117b-6faf-4c37-818c-41e85896f2be /var/tmp       btrfs   subvol=/@tmp,defaults,noatime,compress=zstd,commit=120 0 0
UUID=3086117b-6faf-4c37-818c-41e85896f2be /var/log       btrfs   subvol=/@log,defaults,noatime,compress=zstd,commit=120 0 0
tmpfs                                     /tmp           tmpfs   defaults,noatime,mode=1777 0 0

# PARTICIONES ADICIONALES - CON NOFAIL PARA EVITAR PROBLEMAS DE ARRANQUE
# Esta partición parece estar disponible y funcionando
UUID=265358f9-424c-4a69-b603-e313e372c515 /mnt/265358f9-424c-4a69-b603-e313e372c515 auto nosuid,nodev,nofail,x-gvfs-show 0 0

# PARTICIONES OPCIONALES - Descomentadas solo si las necesitas y están disponibles
# Puedes descomentar estas líneas una por una después de verificar que las particiones existen
#UUID=0e51a690-b95c-4da1-af66-d399119a1290 /mnt/0e51a690-b95c-4da1-af66-d399119a1290 auto nosuid,nodev,nofail,x-gvfs-show 0 0
#UUID=4a9791b2-53fa-48dd-9e4a-062984268936 /mnt/4a9791b2-53fa-48dd-9e4a-062984268936 auto nosuid,nodev,nofail,x-gvfs-show 0 0
#UUID=4d9638bc-6793-4424-b75e-185b95700978 /mnt/4d9638bc-6793-4424-b75e-185b95700978 auto nosuid,nodev,nofail,noauto 0 0

# NOTA: Para verificar qué particiones están disponibles, ejecuta desde Linux:
# lsblk -f
# blkid
# Luego descomenta solo las líneas de las particiones que realmente existen y necesitas
