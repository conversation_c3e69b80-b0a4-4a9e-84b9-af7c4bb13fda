'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = {
  language: 'csp',
  init: function init(Prism) {
    /**
     * Original by <PERSON>.
     *
     * Reference: https://scotthelme.co.uk/csp-cheat-sheet/
     *
     * Supports the following:
     *  - CSP Level 1
     *  - CSP Level 2
     *  - CSP Level 3
     */

    Prism.languages.csp = {
      directive: {
        pattern: /\b(?:(?:base-uri|form-action|frame-ancestors|plugin-types|referrer|reflected-xss|report-to|report-uri|require-sri-for|sandbox) |(?:block-all-mixed-content|disown-opener|upgrade-insecure-requests)(?: |;)|(?:child|connect|default|font|frame|img|manifest|media|object|script|style|worker)-src )/i,
        alias: 'keyword'
      },
      safe: {
        pattern: /'(?:self|none|strict-dynamic|(?:nonce-|sha(?:256|384|512)-)[a-zA-Z\d+=/]+)'/,
        alias: 'selector'
      },
      unsafe: {
        pattern: /(?:'unsafe-inline'|'unsafe-eval'|'unsafe-hashed-attributes'|\*)/,
        alias: 'function'
      }
    };
  }
};