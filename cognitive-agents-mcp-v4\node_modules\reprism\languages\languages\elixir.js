'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = {
  language: 'elixir',
  init: function init(Prism) {
    Prism.languages.elixir = {
      comment: {
        pattern: /#.*/m,
        lookbehind: true
      },
      // ~r"""foo""" (multi-line), ~r'''foo''' (multi-line), ~r/foo/, ~r|foo|, ~r"foo", ~r'foo', ~r(foo), ~r[foo], ~r{foo}, ~r<foo>
      regex: {
        pattern: /~[rR](?:("""|''')(?:\\[\s\S]|(?!\1)[^\\])+\1|([\/|"'])(?:\\.|(?!\2)[^\\\r\n])+\2|\((?:\\.|[^\\)\r\n])+\)|\[(?:\\.|[^\\\]\r\n])+\]|\{(?:\\.|[^\\}\r\n])+\}|<(?:\\.|[^\\>\r\n])+>)[uismxfr]*/,
        greedy: true
      },
      string: [{
        // ~s"""foo""" (multi-line), ~s'''foo''' (multi-line), ~s/foo/, ~s|foo|, ~s"foo", ~s'foo', ~s(foo), ~s[foo], ~s{foo} (with interpolation care), ~s<foo>
        pattern: /~[cCsSwW](?:("""|''')(?:\\[\s\S]|(?!\1)[^\\])+\1|([\/|"'])(?:\\.|(?!\2)[^\\\r\n])+\2|\((?:\\.|[^\\)\r\n])+\)|\[(?:\\.|[^\\\]\r\n])+\]|\{(?:\\.|#\{[^}]+\}|[^\\}\r\n])+\}|<(?:\\.|[^\\>\r\n])+>)[csa]?/,
        greedy: true,
        inside: {
          // See interpolation below
        }
      }, {
        pattern: /("""|''')[\s\S]*?\1/,
        greedy: true,
        inside: {
          // See interpolation below
        }
      }, {
        // Multi-line strings are allowed
        pattern: /("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,
        greedy: true,
        inside: {
          // See interpolation below
        }
      }],
      atom: {
        // Look-behind prevents bad highlighting of the :: operator
        pattern: /(^|[^:]):\w+/,
        lookbehind: true,
        alias: 'symbol'
      },
      // Look-ahead prevents bad highlighting of the :: operator
      'attr-name': /\w+:(?!:)/,
      capture: {
        // Look-behind prevents bad highlighting of the && operator
        pattern: /(^|[^&])&(?:[^&\s\d()][^\s()]*|(?=\())/,
        lookbehind: true,
        alias: 'function'
      },
      argument: {
        // Look-behind prevents bad highlighting of the && operator
        pattern: /(^|[^&])&\d+/,
        lookbehind: true,
        alias: 'variable'
      },
      attribute: {
        pattern: /@[\S]+/,
        alias: 'variable'
      },
      number: /\b(?:0[box][a-f\d_]+|\d[\d_]*)(?:\.[\d_]+)?(?:e[+-]?[\d_]+)?\b/i,
      keyword: /\b(?:after|alias|and|case|catch|cond|def(?:callback|exception|impl|module|p|protocol|struct)?|do|else|end|fn|for|if|import|not|or|require|rescue|try|unless|use|when)\b/,
      boolean: /\b(?:true|false|nil)\b/,
      operator: [/\bin\b|&&?|\|[|>]?|\\\\|::|\.\.\.?|\+\+?|-[->]?|<[-=>]|>=|!==?|\B!|=(?:==?|[>~])?|[*\/^]/, {
        // We don't want to match <<
        pattern: /([^<])<(?!<)/,
        lookbehind: true
      }, {
        // We don't want to match >>
        pattern: /([^>])>(?!>)/,
        lookbehind: true
      }],
      punctuation: /<<|>>|[.,%\[\]{}()]/
    };

    Prism.languages.elixir.string.forEach(function (o) {
      o.inside = {
        interpolation: {
          pattern: /#\{[^}]+\}/,
          inside: {
            delimiter: {
              pattern: /^#\{|\}$/,
              alias: 'punctuation'
            },
            rest: Prism.languages.elixir
          }
        }
      };
    });
  }
};