'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = {
  language: 'erb',
  init: function init(Prism) {
    (function (Prism) {
      Prism.languages.erb = Prism.languages.extend('ruby', {});
      Prism.languages.insertBefore('erb', 'comment', {
        delimiter: {
          pattern: /^<%=?|%>$/,
          alias: 'punctuation'
        }
      });

      Prism.hooks.add('before-tokenize', function (env) {
        var erbPattern = /<%=?[\s\S]+?%>/g;
        Prism.languages['markup-templating'].buildPlaceholders(env, 'erb', erbPattern);
      });

      Prism.hooks.add('after-tokenize', function (env) {
        Prism.languages['markup-templating'].tokenizePlaceholders(env, 'erb');
      });
    })(Prism);
  }
};