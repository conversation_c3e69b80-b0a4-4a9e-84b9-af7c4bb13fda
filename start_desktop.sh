#!/bin/bash
# Script de emergencia para iniciar el escritorio manualmente

echo "=== SCRIPT DE EMERGENCIA PARA INICIAR ESCRITORIO ==="

# Función para iniciar GNOME
start_gnome() {
    echo "Iniciando GNOME..."
    export XDG_SESSION_TYPE=x11
    export GDK_BACKEND=x11
    exec gnome-session
}

# Función para iniciar Plasma
start_plasma() {
    echo "Iniciando Plasma..."
    export XDG_SESSION_TYPE=x11
    export QT_QPA_PLATFORM=xcb
    exec startplasma-x11
}

# Función para iniciar Hyprland
start_hyprland() {
    echo "Iniciando Hyprland..."
    export XDG_SESSION_TYPE=wayland
    exec Hyprland
}

# Función para iniciar SDDM manualmente
start_sddm() {
    echo "Iniciando SDDM..."
    sudo systemctl stop sddm
    sudo systemctl start sddm
}

# Menú de opciones
echo "Selecciona una opción:"
echo "1) Iniciar SDDM"
echo "2) Iniciar GNOME (X11)"
echo "3) Iniciar Plasma (X11)"
echo "4) Iniciar Hyprland (Wayland)"
echo "5) Iniciar X11 básico"
echo "6) Diagnóstico completo"

read -p "Opción (1-6): " option

case $option in
    1)
        start_sddm
        ;;
    2)
        startx /usr/bin/gnome-session
        ;;
    3)
        startx /usr/bin/startplasma-x11
        ;;
    4)
        start_hyprland
        ;;
    5)
        startx
        ;;
    6)
        /root/fix_graphics.sh
        ;;
    *)
        echo "Opción inválida"
        ;;
esac
