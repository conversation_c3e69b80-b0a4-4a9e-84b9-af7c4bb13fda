'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = {
  language: 'smalltalk',
  init: function init(Prism) {
    Prism.languages.smalltalk = {
      comment: /"(?:""|[^"])+"/,
      string: /'(?:''|[^'])+'/,
      symbol: /#[\da-z]+|#(?:-|([+\/\\*~<>=@%|&?!])\1?)|#(?=\()/i,
      'block-arguments': {
        pattern: /(\[\s*):[^\[|]*\|/,
        lookbehind: true,
        inside: {
          variable: /:[\da-z]+/i,
          punctuation: /\|/
        }
      },
      'temporary-variables': {
        pattern: /\|[^|]+\|/,
        inside: {
          variable: /[\da-z]+/i,
          punctuation: /\|/
        }
      },
      keyword: /\b(?:nil|true|false|self|super|new)\b/,
      character: {
        pattern: /\$./,
        alias: 'string'
      },
      number: [/\d+r-?[\dA-Z]+(?:\.[\dA-Z]+)?(?:e-?\d+)?/, /\b\d+(?:\.\d+)?(?:e-?\d+)?/],
      operator: /[<=]=?|:=|~[~=]|\/\/?|\\\\|>[>=]?|[!^+\-*&|,@]/,
      punctuation: /[.;:?\[\](){}]/
    };
  }
};