'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = {
  language: 'c',
  init: function init(Prism) {
    Prism.languages.c = Prism.languages.extend('clike', {
      keyword: /\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|asm|typeof|inline|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|union|unsigned|void|volatile|while)\b/,
      operator: /-[>-]?|\+\+?|!=?|<<?=?|>>?=?|==?|&&?|\|\|?|[~^%?*\/]/,
      number: /(?:\b0x[\da-f]+|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?)[ful]*/i
    });

    Prism.languages.insertBefore('c', 'string', {
      macro: {
        // allow for multiline macro definitions
        // spaces after the # character compile fine with gcc
        pattern: /(^\s*)#\s*[a-z]+(?:[^\r\n\\]|\\(?:\r\n|[\s\S]))*/im,
        lookbehind: true,
        alias: 'property',
        inside: {
          // highlight the path of the include statement as a string
          string: {
            pattern: /(#\s*include\s*)(?:<.+?>|("|')(?:\\?.)+?\2)/,
            lookbehind: true
          },
          // highlight macro directives as keywords
          directive: {
            pattern: /(#\s*)\b(?:define|defined|elif|else|endif|error|ifdef|ifndef|if|import|include|line|pragma|undef|using)\b/,
            lookbehind: true,
            alias: 'keyword'
          }
        }
      },
      // highlight predefined macros as constants
      constant: /\b(?:__FILE__|__LINE__|__DATE__|__TIME__|__TIMESTAMP__|__func__|EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|stdin|stdout|stderr)\b/
    });

    delete Prism.languages.c['class-name'];
    delete Prism.languages.c.boolean;
  }
};