#!/bin/bash
# Script completo para reparar el sistema gráfico

echo "=== DIAGNÓSTICO Y REPARACIÓN DEL SISTEMA GRÁFICO ==="

# 1. Información del sistema
echo "1. Información del hardware gráfico:"
lspci | grep -i vga
lspci | grep -i 3d

# 2. Verificar drivers cargados
echo -e "\n2. Drivers gráficos cargados:"
lsmod | grep -E "(amdgpu|radeon|nvidia|nouveau|i915)"

# 3. Verificar estado de SDDM
echo -e "\n3. Estado de SDDM:"
systemctl status sddm.service --no-pager

# 4. Verificar logs de SDDM
echo -e "\n4. Últimos logs de SDDM:"
journalctl -u sddm.service --no-pager -n 20

# 5. Verificar Xorg
echo -e "\n5. Verificando Xorg:"
which Xorg
ls -la /usr/bin/X*

# 6. Reparaciones automáticas
echo -e "\n=== APLICANDO REPARACIONES ==="

# Regenerar configuración de GRUB
echo "Regenerando GRUB..."
grub-mkconfig -o /boot/grub/grub.cfg

# Regenerar initramfs
echo "Regenerando initramfs..."
mkinitcpio -P

# Reinstalar SDDM
echo "Reinstalando SDDM..."
pacman -S --noconfirm sddm

# Habilitar SDDM
echo "Habilitando SDDM..."
systemctl enable sddm.service

# Crear configuración básica de X11
echo "Configurando X11..."
cat > /etc/X11/xorg.conf.d/20-amdgpu.conf << 'EOF'
Section "Device"
    Identifier "AMD"
    Driver "amdgpu"
    Option "AccelMethod" "glamor"
    Option "DRI" "3"
    Option "TearFree" "true"
EndSection
EOF

# Verificar permisos
echo "Verificando permisos..."
chmod +x /usr/bin/sddm
chmod +x /usr/bin/Xorg

# Limpiar cache
echo "Limpiando cache..."
rm -rf /tmp/.X*
rm -rf /var/lib/sddm/.cache/*

echo -e "\n=== REPARACIÓN COMPLETADA ==="
echo "Reinicia el sistema con: sudo reboot"
echo ""
echo "Si aún tienes problemas:"
echo "1. Presiona Ctrl+Alt+F2 para TTY"
echo "2. Ejecuta: sudo systemctl start sddm"
echo "3. O ejecuta: startx"
