'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = {
  language: 'php',
  init: function init(Prism) {
    /**
     * Original by <PERSON>: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/
     * Modified by <PERSON>: http://milesj.me
     *
     * Supports the following:
     * 		- Extends clike syntax
     * 		- Support for PHP 5.3+ (namespaces, traits, generators, etc)
     * 		- Smarter constant and function matching
     *
     * Adds the following new token classes:
     * 		constant, delimiter, variable, function, package
     */
    (function (Prism) {
      Prism.languages.php = Prism.languages.extend('clike', {
        keyword: /\b(?:and|or|xor|array|as|break|case|cfunction|class|const|continue|declare|default|die|do|else|elseif|enddeclare|endfor|endforeach|endif|endswitch|endwhile|extends|for|foreach|function|include|include_once|global|if|new|return|static|switch|use|require|require_once|var|while|abstract|interface|public|implements|private|protected|parent|throw|null|echo|print|trait|namespace|final|yield|goto|instanceof|finally|try|catch)\b/i,
        constant: /\b[A-Z0-9_]{2,}\b/,
        comment: {
          pattern: /(^|[^\\])(?:\/\*[\s\S]*?\*\/|\/\/.*)/,
          lookbehind: true
        }
      });

      Prism.languages.insertBefore('php', 'string', {
        'shell-comment': {
          pattern: /(^|[^\\])#.*/,
          lookbehind: true,
          alias: 'comment'
        }
      });

      Prism.languages.insertBefore('php', 'keyword', {
        delimiter: {
          pattern: /\?>|<\?(?:php|=)?/i,
          alias: 'important'
        },
        variable: /\$+(?:\w+\b|(?={))/i,
        package: {
          pattern: /(\\|namespace\s+|use\s+)[\w\\]+/,
          lookbehind: true,
          inside: {
            punctuation: /\\/
          }
        }
      });

      // Must be defined after the function pattern
      Prism.languages.insertBefore('php', 'operator', {
        property: {
          pattern: /(->)[\w]+/,
          lookbehind: true
        }
      });

      Prism.languages.insertBefore('php', 'string', {
        'nowdoc-string': {
          pattern: /<<<'([^']+)'(?:\r\n?|\n)(?:.*(?:\r\n?|\n))*?\1;/,
          greedy: true,
          alias: 'string',
          inside: {
            delimiter: {
              pattern: /^<<<'[^']+'|[a-z_]\w*;$/i,
              alias: 'symbol',
              inside: {
                punctuation: /^<<<'?|[';]$/
              }
            }
          }
        },
        'heredoc-string': {
          pattern: /<<<(?:"([^"]+)"(?:\r\n?|\n)(?:.*(?:\r\n?|\n))*?\1;|([a-z_]\w*)(?:\r\n?|\n)(?:.*(?:\r\n?|\n))*?\2;)/i,
          greedy: true,
          alias: 'string',
          inside: {
            delimiter: {
              pattern: /^<<<(?:"[^"]+"|[a-z_]\w*)|[a-z_]\w*;$/i,
              alias: 'symbol',
              inside: {
                punctuation: /^<<<"?|[";]$/
              }
            },
            interpolation: null // See below
          }
        },
        'single-quoted-string': {
          pattern: /'(?:\\[\s\S]|[^\\'])*'/,
          greedy: true,
          alias: 'string'
        },
        'double-quoted-string': {
          pattern: /"(?:\\[\s\S]|[^\\"])*"/,
          greedy: true,
          alias: 'string',
          inside: {
            interpolation: null // See below
          }
        }
      });
      // The different types of PHP strings "replace" the C-like standard string
      delete Prism.languages.php.string;

      var string_interpolation = {
        pattern: /{\$(?:{(?:{[^{}]+}|[^{}]+)}|[^{}])+}|(^|[^\\{])\$+(?:\w+(?:\[.+?]|->\w+)*)/,
        lookbehind: true,
        inside: {
          rest: Prism.languages.php
        }
      };
      Prism.languages.php['heredoc-string'].inside.interpolation = string_interpolation;
      Prism.languages.php['double-quoted-string'].inside.interpolation = string_interpolation;

      Prism.hooks.add('before-tokenize', function (env) {
        if (!/(?:<\?php|<\?)/gi.test(env.code)) {
          return;
        }

        var phpPattern = /(?:<\?php|<\?)[\s\S]*?(?:\?>|$)/gi;
        Prism.languages['markup-templating'].buildPlaceholders(env, 'php', phpPattern);
      });

      Prism.hooks.add('after-tokenize', function (env) {
        Prism.languages['markup-templating'].tokenizePlaceholders(env, 'php');
      });
    })(Prism);
  }
};